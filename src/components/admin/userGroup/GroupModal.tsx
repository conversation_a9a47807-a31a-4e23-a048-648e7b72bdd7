import React, { useState, useEffect } from "react";
import { XMarkIcon, UserPlusIcon } from "@heroicons/react/24/outline";
import SearchBar from "../SearchBar";
import { useGetGroupByID } from "@/hook/admin/group/useGetGroupByID";
import { useGetAllUserExt } from "@/hook/admin/useGetAllUserExt";
import { User_ext } from "@/types/LMSTypes";
import { useAddGroup } from "@/hook/admin/group/useAddGroup";
import { useUpdateGroup } from "@/hook/admin/group/useUpdateGroup";
import { AddGroup } from "@/types/LMSTypes";
import { UpdateGroup } from "@/types/LMSTypes";
import { useGetSearchedUsers } from "@/hook/admin/useGetSearchedUsers";


interface FormErrors {
  [key: string]: string;
}

const GroupModal = ({
  onClose,
  onSubmit,
  groupID, // Define the groupID prop
  type
}: {
  onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  onSubmit: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  groupID: number; // Define the type of groupID
  type: string;
}) => {
  const [search, setSearch] = useState("");
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const { data: groupData, isLoading: isLoadingGroup, isError: isErrorGroup } = useGetGroupByID(groupID);
  const { data: userData, isLoading: isLoadingUser, isError: isErrorUser } = useGetAllUserExt();
  const [selectedGroupName, setselectedGroupName] = useState()
  const [selectedGroupFAQ, setselectedGroupFAQ] = useState()
  const [selectedGroupID, setSelectedGroupID] = useState(Number)
  const [selectedisActiveState, setSelectedisActiveState] = useState(Boolean)
  const [selectedGroupAdmin, setSelectedGroupAdmin] = useState(String)
  const [selectedGroupAdminId, setselectedGroupAdminId] = useState(groupData?.group_admin_user_id)
  const [isActive, setIsActive] = useState(Number)
  const [submitState, setSubmitState] = useState("Save");
  const [errors, setErrors] = useState<FormErrors>({});

  console.log("editiong values", groupData)

  const details: AddGroup = {
    group_name: selectedGroupName,
    created_by: selectedGroupAdmin,
    group_faq: selectedGroupFAQ,
    group_admin_user_id: selectedGroupAdminId
  }

  const updateGroupData: UpdateGroup = {
    group_id: groupID,
    created_by: selectedGroupAdmin,
    group_name: selectedGroupName,
    group_faq: selectedGroupFAQ,
    group_admin_user_id: selectedGroupAdminId,
    isactive: selectedisActiveState ? 1 : 0
  }

  const addingGroup = useAddGroup(details); // Use the hook here
  const updatingGroup = useUpdateGroup(updateGroupData); // Use the hook here

  const { data: searchedUsers } = useGetSearchedUsers(search);

  // console.log("groupID", groupID)

  useEffect(() => {
    if (groupData) {
      setselectedGroupName(groupData.group_name || "");
      setselectedGroupFAQ(groupData.group_faq || "");
      setselectedGroupAdminId(groupData.group_admin_user_id || null);
      setSelectedisActiveState(groupData.isactive === 1);
      if (userData) {
        const admin = userData.find(user => user.user_id === groupData.group_admin_user_id);
        setSelectedGroupAdmin(admin ? admin.user_full_name : "");
      }
    }
  }, [groupData, userData]);

  useEffect(() => {
    if (!isLoadingGroup && !isErrorGroup) {
      console.log('groupData:', groupData);
    }
    if (!isLoadingUser && !isErrorUser) {
      console.log('userData:', userData);
    }
  }, [groupID, isLoadingGroup, isErrorGroup, userData, isLoadingUser, isErrorUser]);

  if (isLoadingGroup || isLoadingUser) {
    return <div>Loading...</div>;
  }

  if (isErrorGroup || isErrorUser) {
    return <div>Error fetching data</div>;
  }


  const handleChangeActiveState = () => {
    if (selectedisActiveState === true) {
      setSelectedisActiveState(false)
    } else if (selectedisActiveState === false) {
      setSelectedisActiveState(true)
    }
  }

  function findCommonObjects(array1, array2) {
    // Check if array2 is not an array or undefined, return array1
    if (!Array.isArray(array2) || typeof array2 !== 'object') {
      return array1;
    }

    const commonObjects = [];

    // Iterate through the first array
    for (let obj1 of array1) {
      // Check if the object exists in the second array
      const found = array2.find(obj2 => JSON.stringify(obj1) === JSON.stringify(obj2));
      if (found) {
        commonObjects.push(obj1);
      }
    }

    // If no common objects found, return all objects from array1
    if (commonObjects.length === 0) {
      return array1;
    }

    return commonObjects;
  }

  const userFilteredValues = findCommonObjects(userData, searchedUsers)

  // console.log("searched users values:- ", userFilteredValues)

  const handleChangeUserDetails = (user_id: number, user_full_name: string) => {
    if (selectedGroupAdminId === user_id && selectedUserId === user_id) {
      setSelectedGroupAdmin("");
      setselectedGroupAdminId(0);
      setSelectedUserId(0);
    } else {
      setSelectedGroupAdmin(user_full_name);
      setselectedGroupAdminId(user_id);
      setSelectedUserId(user_id);
    }
  };


  const handleSearch = (userSearch: String) => {
    //Call the filter content API and use the filtered content here
    console.log("Check for content", userSearch);
    setSearch(userSearch);
    console.log("search things:- ", search)
  };

  // // Function to handle checkbox change
  // const handleCheckboxChange = (userId: number) => {
  //   // Toggle selection: if the clicked checkbox's user is already selected as Admin, deselect it, otherwise select it
  //   setSelectedUserId((prevSelectedUserId) =>
  //     prevSelectedUserId === userId ? null : userId
  //   )
  // };

  const handleSubmitDetails = async (type: string, event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault(); // Prevent default form submission behavior

    const newErrors: FormErrors = {};

    if (!selectedGroupName || selectedGroupName.length < 3) {
      newErrors.groupName = "Please enter a valid group name";
    }

    if (!selectedGroupAdmin || selectedGroupAdmin.length < 2) {
      newErrors.groupAdmin = "Please select a group Admin from below";
    }

    if (!selectedGroupFAQ || selectedGroupFAQ.length < 2) {
      newErrors.groupFAQ = "Please enter a FAQ for group";
    }

    setErrors(newErrors);

    // Only proceed if there are no errors
    if (Object.keys(newErrors).length === 0) {
      try {
        setSubmitState("Loading");
        if (type === "create") {
          await addingGroup.mutate(details);
        } else if (type === "edit") {
          await updatingGroup.mutate(updateGroupData);
        }
        setSubmitState("Save");
        onSubmit();
      } catch (error) {
        console.error("Error handling group:", error);
      }
    }
  };

  if (type == "createGroup") {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
          <form onSubmit={(e) => handleSubmitDetails("create", e)} className="flex flex-col h-full min-h-0">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <UserPlusIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Create New Group</h2>
                  <p className="text-sm text-gray-500">Set up a new user group with admin assignment</p>
                </div>
              </div>
              <button
                type="button"
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Form Content */}
            <div className="flex-1 overflow-y-auto p-6 space-y-6 min-h-0">
              {/* Basic Information */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Group Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    className={`w-full px-3 py-2.5 text-sm border rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 ${
                      errors.groupName ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
                    }`}
                    placeholder="Enter group name"
                    onChange={(e) => setselectedGroupName(e.target.value)}
                  />
                  {errors.groupName && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <span className="mr-1">⚠</span>
                      {errors.groupName}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Group Admin <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    className={`w-full px-3 py-2.5 text-sm border rounded-lg shadow-sm bg-gray-50 transition-colors duration-200 ${
                      errors.groupAdmin ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Select admin from list below"
                    value={selectedGroupAdmin || ''}
                    disabled
                    onChange={(e) => setSelectedGroupAdmin(e.target.value)}
                  />
                  {errors.groupAdmin && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <span className="mr-1">⚠</span>
                      {errors.groupAdmin}
                    </p>
                  )}
                </div>
              </div>

              {/* Program Details */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Program Details
                </label>
                <textarea
                  className={`w-full px-3 py-2.5 text-sm border rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 resize-none ${
                    errors.groupFAQ ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
                  }`}
                  rows={3}
                  placeholder="Enter program details and description"
                  onChange={(e) => setselectedGroupFAQ(e.target.value)}
                />
                {errors.groupFAQ && (
                  <p className="text-red-500 text-xs mt-1 flex items-center">
                    <span className="mr-1">⚠</span>
                    {errors.groupFAQ}
                  </p>
                )}
              </div>

              {/* Admin Selection */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <label className="block text-sm font-medium text-gray-700">
                    Select Group Admin <span className="text-red-500">*</span>
                  </label>
                  <div className="w-64">
                    <SearchBar onSearch={handleSearch} />
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <div className="max-h-48 overflow-y-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50 sticky top-0">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            User Details
                          </th>
                          <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Select
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {userFilteredValues?.map((userValue: User_ext) => (
                          <tr
                            key={userValue.user_id}
                            className={`hover:bg-gray-50 transition-colors duration-150 cursor-pointer ${
                              selectedUserId === userValue.user_id ? 'bg-blue-50 border-blue-200' : ''
                            }`}
                            onClick={() => handleChangeUserDetails(userValue.user_id, userValue.user_full_name)}
                          >
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-8 w-8 mr-3">
                                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center">
                                    <span className="text-xs font-medium text-white">
                                      {userValue.user_full_name?.charAt(0)?.toUpperCase() || 'U'}
                                    </span>
                                  </div>
                                </div>
                                <div>
                                  <div className="text-sm font-medium text-gray-900">
                                    {userValue.user_full_name}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    ID: {userValue.user_id}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                              <input
                                type="radio"
                                id={userValue.user_id.toString()}
                                name="groupAdmin"
                                checked={selectedUserId === userValue.user_id}
                                onChange={() => handleChangeUserDetails(userValue.user_id, userValue.user_full_name)}
                                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50 flex-shrink-0">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-6 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-[1.02]"
              >
                Create Group
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  } else if (type == "editGroup") {
    const admin = userData?.find(user => user.user_id === groupData?.group_admin_user_id);

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
          <form onSubmit={(e) => handleSubmitDetails("edit", e)} className="flex flex-col h-full min-h-0">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <UserPlusIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Edit Group</h2>
                  <p className="text-sm text-gray-500">Update group information and settings</p>
                </div>
              </div>
              <button
                type="button"
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Form Content */}
            <div className="flex-1 overflow-y-auto p-6 space-y-6 min-h-0">
              {/* Basic Information */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Group Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    required
                    className={`w-full px-3 py-2.5 text-sm border rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 ${
                      errors.groupName ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
                    }`}
                    placeholder="Enter group name"
                    value={selectedGroupName || groupData?.group_name || ''}
                    onChange={(e) => setselectedGroupName(e.target.value)}
                  />
                  {errors.groupName && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <span className="mr-1">⚠</span>
                      {errors.groupName}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Group Admin <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    required
                    className={`w-full px-3 py-2.5 text-sm border rounded-lg shadow-sm bg-gray-50 transition-colors duration-200 ${
                      errors.groupAdmin ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Select admin from list below"
                    value={selectedGroupAdmin || admin?.user_full_name || ''}
                    disabled
                    onChange={(e) => setSelectedGroupAdmin(e.target.value)}
                  />
                  {errors.groupAdmin && (
                    <p className="text-red-500 text-xs mt-1 flex items-center">
                      <span className="mr-1">⚠</span>
                      {errors.groupAdmin}
                    </p>
                  )}
                </div>
              </div>

              {/* Program Details */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Program Details
                </label>
                <textarea
                  className={`w-full px-3 py-2.5 text-sm border rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 resize-none ${
                    errors.groupFAQ ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
                  }`}
                  rows={3}
                  placeholder="Enter program details and description"
                  value={selectedGroupFAQ || groupData?.group_faq || ''}
                  onChange={(e) => setselectedGroupFAQ(e.target.value)}
                />
                {errors.groupFAQ && (
                  <p className="text-red-500 text-xs mt-1 flex items-center">
                    <span className="mr-1">⚠</span>
                    {errors.groupFAQ}
                  </p>
                )}
              </div>

              {/* Status and Search */}
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-end gap-4">
                <div className="w-64">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Search Admin
                  </label>
                  <SearchBar onSearch={handleSearch} />
                </div>

                <div className="flex items-center space-x-3">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="sr-only"
                      checked={selectedisActiveState}
                      onChange={() => handleChangeActiveState()}
                    />
                    <div className={`relative w-9 h-5 transition-colors duration-200 ease-in-out rounded-full ${
                      selectedisActiveState ? 'bg-blue-600' : 'bg-gray-300'
                    }`}>
                      <div className={`absolute top-0.5 left-0.5 w-4 h-4 bg-white rounded-full shadow-sm transition-transform duration-200 ease-in-out ${
                        selectedisActiveState ? 'translate-x-4' : 'translate-x-0'
                      }`} />
                    </div>
                    <span className="ml-3 text-sm font-medium text-gray-700">
                      Group is active
                    </span>
                  </label>
                </div>
              </div>

              {/* Admin Selection */}
              <div className="space-y-4">
                <label className="block text-sm font-medium text-gray-700">
                  Change Group Admin (Optional)
                </label>

                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <div className="max-h-48 overflow-y-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50 sticky top-0">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            User Details
                          </th>
                          <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Select
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {userFilteredValues?.map((userValue: User_ext) => (
                          <tr
                            key={userValue.user_id}
                            className={`hover:bg-gray-50 transition-colors duration-150 cursor-pointer ${
                              selectedUserId === userValue.user_id ? 'bg-blue-50 border-blue-200' : ''
                            }`}
                            onClick={() => handleChangeUserDetails(userValue.user_id, userValue.user_full_name)}
                          >
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-8 w-8 mr-3">
                                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center">
                                    <span className="text-xs font-medium text-white">
                                      {userValue.user_full_name?.charAt(0)?.toUpperCase() || 'U'}
                                    </span>
                                  </div>
                                </div>
                                <div>
                                  <div className="text-sm font-medium text-gray-900">
                                    {userValue.user_full_name}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    ID: {userValue.user_id}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                              <input
                                type="radio"
                                id={userValue.user_id.toString()}
                                name="groupAdmin"
                                checked={selectedUserId === userValue.user_id}
                                onChange={() => handleChangeUserDetails(userValue.user_id, userValue.user_full_name)}
                                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50 flex-shrink-0">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-6 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-[1.02]"
              >
                {submitState || 'Update Group'}
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }


};

export default GroupModal;