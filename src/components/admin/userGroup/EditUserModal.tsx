import React, { useState, FormEvent } from "react";
import { XMarkIcon, UserIcon, EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import { toast } from 'react-toastify';
import { useUpdateUser } from "@/hook/admin/useUpdateUser";
import { UpdateUser } from "@/types/LMSTypes";

interface EditUserModalProps {
  onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  onSubmit: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  userValueTemp: {
    user_id: number;
    user_full_name: string;
    user: {
      phone: string;
      email: string;
    };
  };
}

interface FormErrors {
  [key: string]: string;
}

export default function EditUserModal({ onClose, userValueTemp, onSubmit }: EditUserModalProps) {
  // State for each input field
  const [name, setName] = useState(userValueTemp.user_full_name || "");
  const [phoneNumber, setPhoneNumber] = useState(userValueTemp.user?.phone || "");
  const [email, setEmail] = useState(userValueTemp.user?.email || "");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [warning, setWarning] = useState('');

  console.log("userIDValues 2:-", userValueTemp);

  const changeUser: UpdateUser = {
    user_id: userValueTemp.user_id,
    ...(email && email !== userValueTemp.user?.email && { email }),
    ...(password && { password }),
    ...(name && name !== userValueTemp.user_full_name && { user_full_name: name }),
    ...(phoneNumber && phoneNumber !== userValueTemp.user?.phone && { phone: phoneNumber })
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};
    
    if (!name.trim()) newErrors.name = "Name is required";
    if (!email.trim()) newErrors.email = "Email is required";
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email))
      newErrors.email = "Invalid email format";
    if (!phoneNumber) newErrors.phoneNumber = "Phone number is required";
    if (password && password.length < 6) newErrors.password = "Password must be at least 6 characters";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let input = e.target.value;

    // Remove non-digit characters except the + at the beginning
    let formattedPhoneNumber = input.replace(/[^\d+]/g, '');

    // Ensure it starts with +91
    if (!formattedPhoneNumber.startsWith('+91')) {
      formattedPhoneNumber = '+91' + formattedPhoneNumber.replace(/\D/g, '');
    }

    // Extract the phone number part after +91
    let phonePart = formattedPhoneNumber.slice(3);

    // Check if the phone number part starts with 0
    if (phonePart.length > 0 && phonePart.charAt(0) === '0') {
      setWarning('Phone number cannot start with 0.');
    } else if (phonePart.length > 10) {
      setWarning('Phone number must be 10 digits.');
    } else {
      setWarning('');
      if (errors.phoneNumber) {
        setErrors({ ...errors, phoneNumber: '' });
      }
    }

    // Limit to 10 digits for the phone number part
    if (phonePart.length > 10) {
      phonePart = phonePart.slice(0, 10);
    }

    setPhoneNumber('+91' + phonePart);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (errors.email) {
      setErrors({ ...errors, email: '' });
    }
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
    if (errors.name) {
      setErrors({ ...errors, name: '' });
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    if (errors.password) {
      setErrors({ ...errors, password: '' });
    }
  };

  const updatingUser = useUpdateUser(changeUser);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    const phoneDigits = phoneNumber.replace(/^\+91/, '');
    if (phoneDigits.length < 10 || phoneDigits.length > 10) {
      setErrors({ ...errors, phoneNumber: "Please enter a correct phone number" });
      return;
    }

    setIsSubmitting(true);

    try {
      await updatingUser.mutate();
      toast.success('User updated successfully!');
      onSubmit();
    } catch (error) {
      console.error("Error updating user data:", error);
      toast.error('Error updating user. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-5 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
              <UserIcon className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Edit User</h3>
              <p className="text-sm text-gray-500">Update user information</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-lg p-1.5 transition-colors duration-200"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Current User Info */}
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0 h-10 w-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-white">
                {userValueTemp.user_full_name?.charAt(0)?.toUpperCase() || 'U'}
              </span>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-900">{userValueTemp.user_full_name}</h4>
              <p className="text-xs text-gray-500">{userValueTemp.user?.email}</p>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-5">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
            {/* Name Field */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1.5">
                Full Name *
              </label>
              <input
                id="name"
                type="text"
                value={name}
                onChange={handleNameChange}
                className={`w-full px-3 py-2.5 text-sm border rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 ${
                  errors.name ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                }`}
                placeholder={userValueTemp.user_full_name}
              />
              {errors.name && (
                <p className="mt-1 text-xs text-red-600 flex items-center">
                  <span className="mr-1">⚠</span>
                  {errors.name}
                </p>
              )}
            </div>

            {/* Phone Field */}
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1.5">
                Phone Number *
              </label>
              <input
                id="phone"
                type="text"
                value={phoneNumber}
                onChange={handlePhoneChange}
                className={`w-full px-3 py-2.5 text-sm border rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 ${
                  errors.phoneNumber || warning ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                }`}
                placeholder={userValueTemp.user?.phone}
              />
              {(warning || errors.phoneNumber) && (
                <p className="mt-1 text-xs text-red-600 flex items-center">
                  <span className="mr-1">⚠</span>
                  {warning || errors.phoneNumber}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1.5">
                Email Address *
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={handleEmailChange}
                className={`w-full px-3 py-2.5 text-sm border rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 ${
                  errors.email ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                }`}
                placeholder={userValueTemp.user?.email}
              />
              {errors.email && (
                <p className="mt-1 text-xs text-red-600 flex items-center">
                  <span className="mr-1">⚠</span>
                  {errors.email}
                </p>
              )}
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1.5">
                New Password (optional)
              </label>
              <div className="relative">
                <input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={handlePasswordChange}
                  className={`w-full px-3 py-2.5 pr-10 text-sm border rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 ${
                    errors.password ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Leave blank to keep current password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-500"
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-4 w-4" />
                  ) : (
                    <EyeIcon className="h-4 w-4" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-xs text-red-600 flex items-center">
                  <span className="mr-1">⚠</span>
                  {errors.password}
                </p>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className={`flex-1 px-4 py-2.5 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 ${
                isSubmitting
                  ? 'bg-blue-400 cursor-not-allowed'
                  : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transform hover:scale-[1.02]'
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                  Updating...
                </div>
              ) : (
                'Update User'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}