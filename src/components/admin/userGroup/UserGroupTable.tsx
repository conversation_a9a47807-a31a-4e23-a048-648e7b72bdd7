import React, { useState, useEffect, useRef } from "react";
import BulkQuesModal from "../BulkQuesModal";
import Link from "next/link";
import {
  PencilSquareIcon,
  UserPlusIcon,
  DocumentTextIcon,
  AdjustmentsVerticalIcon,
  FolderIcon,
  CloudArrowDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import DeletedUserModal from "./DeletedUserModal";
import ResetPasswordModal from "./EditUserModal";
import GroupModal from "./GroupModal";
import { useQueryClient } from "@tanstack/react-query";
import { useGetUsersInGroup } from "@/hook/admin/group/useGetUsersInGroup";
import Nothing from "@/components/ui/Nothing";
import { useGetAllUserExt } from "@/hook/admin/useGetAllUserExt";
import { getUsersInGroup, getUsersInGroupDownload } from '@/api/admin/group/getUsersInGroup';
import { useCount } from "@/context/searchStore";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import GroupAssignmentFlow from "@/components/admin/userGroup/GroupAssignmentFlow";

interface UserGroupTableProps {
  userValues: Array<{
    group_id: number;
    group_admin_user_id: number;
    group_name: string;
    isactive: number; 
    created_by: string;
    total_member: string;
  }>;
}

const defaultType = "editGroup";

function getUserFullNameById(data, userId) {
  const user = data?.find((item) => item.user_id === userId);
  return user ? user.user_full_name : null;
}
// Place this above your component definition
const mockGroups = [
  {
    group_id: 1,
    group_admin_user_id: 101,
    group_name: "Frontend Team",
    isactive: 1,
    created_by: "Admin",
    total_member: "4",
  },
  {
    group_id: 2,
    group_admin_user_id: 102,
    group_name: "Backend Team",
    isactive: 1,
    created_by: "Admin",
    total_member: "3",
  },
  {
    group_id: 3,
    group_admin_user_id: 103,
    group_name: "QA Team",
    isactive: 0,
    created_by: "Admin",
    total_member: "2",
  },
];
export default function UserGroupTable() {
//export default function UserGroupTable({ userValues }: UserGroupTableProps) {
  const [isPasswordModal, setIsPasswordModal] = useState(false);
  const [isDeletedUserModalOpen, setIsDeletedUserModalOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [groupId, setgroupId] = useState(Number);
  const { data: allUserExtData } = useGetAllUserExt();
  const [popup1, setPopup1] = useState<boolean>(false)
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
 // const totalPages = Math.ceil(userValues.length / itemsPerPage);
  const totalPages = 1;
  const groupIdRef = useRef<number>(0);
  const userInGroupRefBool = useRef<boolean>(false);
  const { searchValue, searchNumber } = useCount();
  const [showAssignmentFlow, setShowAssignmentFlow] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);

  const handleOpenAssignmentFlow = (groupId: number) => {
  setSelectedGroupId(groupId);
  setShowAssignmentFlow(true);
  };

const handleCloseAssignmentFlow = () => {
    setShowAssignmentFlow(false);
    setSelectedGroupId(null);
 };

  let temp2:boolean = false 

useEffect(() => {
  temp2 = useCount.getState().searchValue
  console.log("search numberss:- ", temp2)
  console.log("search numberss page:- ", currentPage)
  if(temp2==true && currentPage != 1){
    setCurrentPage(1)
  }
}, [searchNumber])


  const { data: groupData } = useGetUsersInGroup(groupIdRef.current);

  console.log("groupData ispppp", groupData)
  console.log("groupIdRef", groupIdRef.current)


  const handleCloseDeleteModal = () => {
    setIsPasswordModal(false);
  };

  useEffect(() => {
    console.log("groupIdRef 2test", groupData)
  }, [groupIdRef.current])



  // const handleOpenDeletedUserModal = () => {
  //   setIsDeletedUserModalOpen(true);
  //   setIsPasswordModal(false);
  // };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    //queryClient.invalidateQueries() //Invalidate every query in cache
  };

  function convertToCSV(jsonData: any) {
    var csv = "";
    var keys = Object.keys(jsonData[0]);

    // Append header
    csv += keys.join(",") + "\n";

    // Append rows
    jsonData.forEach(function (row: any) {
      csv +=
        keys
          .map(function (key) {
            return row[key];
          })
          .join(",") + "\n";
    });
    console.log("Homelander:- ", jsonData)
    console.log("Homelander2:- ", csv)
    return csv;
  }

  // Download CSV file
  function downloadCSV(csvData: any, fileName: string) {
    var blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });

    if (navigator.msSaveBlob) {
      // For IE
      navigator.msSaveBlob(blob, fileName);
    } else {
      var link = document.createElement("a");
      if (link.download !== undefined) {
        var url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", fileName);
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  }

  const handleEditClick = (group_id: number) => {


    setgroupId(group_id);
    groupIdRef.current = group_id
    setIsModalOpen(true);
  };

  const handleDownloadUserInGroupData = async (group_id: any) => {
    userInGroupRefBool.current = true
    console.log("Download...")
    console.log("groupIdRef1", groupIdRef)

    setgroupId(group_id);

    const userGroupData = await getUsersInGroupDownload(groupIdRef.current)

    console.log("temptest:- ", userGroupData)
    try {
      if (userGroupData.length > 0) {
        var csvData = convertToCSV(userGroupData);
        console.log("csvData: ", csvData)
        // Download CSV file
        downloadCSV(csvData, 'data.csv')
      } else {
        console.log("starlight")
        setPopup1(true)
        setTimeout(() => {
          setPopup1(false)
        }, 2000);

      }
    } catch (e) {
      console.log("exception occured", e);
    } finally {
      //no code to execute here
    }
  };
 // console.log(userValues);

  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));
  };

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  // Instead of:
// const currentItems = userValues.slice(indexOfFirstItem, indexOfLastItem);
const currentItems = mockGroups.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      {/* Table Header Stats */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="flex items-center text-sm text-gray-600">
              <UserGroupIcon className="h-5 w-5 mr-2 text-gray-400" />
              <span className="font-medium">{currentItems?.length || 0}</span>
              <span className="ml-1">groups</span>
            </div>
            <div className="text-sm text-gray-500">
              Page {currentPage} of {totalPages}
            </div>
          </div>
          <div className="text-sm text-gray-500">
            Showing {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, mockGroups?.length || 0)} of {mockGroups?.length || 0}
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Group Details</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Assessments</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Modules</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Download</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>

          <tbody className="bg-white divide-y divide-gray-200">
            {Array.isArray(currentItems) && currentItems.length > 0 ? (
              currentItems.map((userValue) => (
                <tr key={userValue.group_id} className="hover:bg-gray-50 transition-colors duration-150">
                  {/* Group Details Column */}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 mr-4">
                        <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center">
                          <span className="text-sm font-medium text-white">
                            {userValue.group_name?.charAt(0)?.toUpperCase() || 'G'}
                          </span>
                        </div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {userValue.group_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          ID: {userValue.group_id}
                        </div>
                      </div>
                    </div>
                  </td>

                  {/* Admin Column */}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {getUserFullNameById(allUserExtData, userValue.group_admin_user_id) || 'Not assigned'}
                    </div>
                  </td>

                  {/* Status Column */}
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      userValue.isactive === 1
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {userValue.isactive === 1 ? 'Active' : 'Inactive'}
                    </span>
                  </td>

                  {/* Assessments Column */}
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <Link
                      href={{
                        pathname: "/admin/usergroup/assessments",
                        query: { groupId: userValue.group_id },
                      }}
                    >
                      <button className="inline-flex items-center p-2 text-sm font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-200" title="Manage Assessments">
                        <DocumentTextIcon className="h-5 w-5" />
                      </button>
                    </Link>
                  </td>

                  {/* Users Column */}
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <Link
                      href={{
                        pathname: "usergroup/alluser",
                        query: {
                          groupId: userValue.group_id,
                          groupName: userValue.group_name,
                        },
                      }}
                    >
                      <button className="inline-flex items-center p-2 text-sm font-medium text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-all duration-200" title="Manage Users">
                        <UserPlusIcon className="h-5 w-5" />
                      </button>
                    </Link>
                  </td>

                  {/* Modules Column */}
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <Link
                      href={{
                        pathname: "/admin/usergroup/addremovemodule",
                        query: { groupId: userValue.group_id },
                      }}
                    >
                      <button className="inline-flex items-center p-2 text-sm font-medium text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded-lg transition-all duration-200" title="Manage Modules">
                        <AdjustmentsVerticalIcon className="h-5 w-5" />
                      </button>
                    </Link>
                  </td>

                  {/* Download Column */}
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <button
                      onClick={() => {
                        groupIdRef.current = userValue.group_id;
                        handleDownloadUserInGroupData(userValue.group_id);
                      }}
                      className="inline-flex items-center p-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-all duration-200"
                      title="Download User Data"
                    >
                      <CloudArrowDownIcon className="h-5 w-5" />
                    </button>
                  </td>

                  {/* Actions Column */}
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <button
                      onClick={() => handleOpenAssignmentFlow(userValue.group_id)}
                      className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-200"
                    >
                      Manage Roles & Users
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="px-6 py-12 text-center">
                  <Nothing
                    title="No Groups Found"
                    para="There are currently no groups to display. Please check back later or create a new group."
                  />
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {mockGroups?.length > 0 && (
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
              <span className="font-medium">{Math.min(indexOfLastItem, mockGroups.length)}</span> of{' '}
              <span className="font-medium">{mockGroups.length}</span> results
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handlePreviousPage}
                disabled={currentPage <= 1}
                className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  currentPage <= 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:text-gray-900 shadow-sm'
                }`}
              >
                <ChevronLeftIcon className="h-4 w-4 mr-1" />
                Previous
              </button>

              <span className="text-sm text-gray-700 px-3 py-2">
                Page {currentPage} of {totalPages}
              </span>

              <button
                onClick={handleNextPage}
                disabled={currentPage >= totalPages}
                className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  currentPage >= totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:text-gray-900 shadow-sm'
                }`}
              >
                Next
                <ChevronRightIcon className="h-4 w-4 ml-1" />
              </button>
            </div>
          </div>
        </div>
      )}
      {isPasswordModal && (
        <ResetPasswordModal onClose={handleCloseDeleteModal} />
      )}
      {isDeletedUserModalOpen && (
        <DeletedUserModal onClose={() => setIsDeletedUserModalOpen(false)} />
      )}
      {isModalOpen && (
        <GroupModal
          onClose={() => {
            handleCloseModal();
          }}
          groupID={groupId}
          type={defaultType}
        />
      )}{
        popup1 && (
          <BulkQuesModal
            modalName={"Error"}
            modalText={"Unable to download User Group Data"}
            onClose={() => setPopup1(false)} />
        )
      }
      <Modal open={showAssignmentFlow} onClose={handleCloseAssignmentFlow}>
      {selectedGroupId && (
        <GroupAssignmentFlow
      groupId={selectedGroupId}
      onClose={handleCloseAssignmentFlow}
    />
  )}
      </Modal>
    </div>
  );
}
