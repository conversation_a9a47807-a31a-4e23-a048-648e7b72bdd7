"use client";
import React, { useState, useRef } from "react";
import SearchBar from "../SearchBar";
import { PlusIcon, MinusIcon } from "@heroicons/react/24/outline";
import Heading from "@/components/ui/Heading";
import { useAddUserToGroup } from "@/hook/admin/group/useAddUserToGroup";
import { AddRemoveUserToGroup } from "@/types/LMSTypes";
import { useRemoveUserFromGroup } from "@/hook/admin/group/useRemoveUserFromGroup";
import { useGetNonUserSearch } from "@/hook/admin/usergroup/alluser/useGetNonUserSearch";
import { useGetUserSearch } from "@/hook/admin/usergroup/alluser/useGetUserSearch";
import { DocumentTextIcon } from "@heroicons/react/24/outline";
import Nothing from "@/components/ui/Nothing";

interface AllUserProps {
  type: string;
  groupId: number;
}

const AllUser = ({ type, groupId }: AllUserProps) => {
  const [search, setsearch] = useState("");
  const [groupIdValue, setGroupIdValue] = useState(groupId);
  const [userIdValue, setuserIdValue] = useState(Number);
  const [search2, setsearch2] = useState("");
  const refreshCount = useRef(0);

  const newDetail: AddRemoveUserToGroup[] = [{
    group_id: groupIdValue,
    user_id: userIdValue
  }]

  const { data: groupUserSearchData } = useGetUserSearch(groupId, search2)

  const { data: nonGroupUserSearchData } = useGetNonUserSearch(groupId, search)

  console.log("nonGroupUserSearchData:-", nonGroupUserSearchData)
  console.log("groupUserSearchData:-", groupUserSearchData)
  // console.log("search:-", search)
  // console.log("search2:-", search2)
  // console.log("Re-render:-")
  // console.log("useRef:-", refreshCount.current)
  const addingUserToGroup = useAddUserToGroup(newDetail);

  const removeUserToGroup = useRemoveUserFromGroup(newDetail);

  const handleClickUser = async (type: string) => {
    if (type === "add") {
      try {
        await addingUserToGroup.mutate();
        setTimeout(() => { }, 1000);
        console.log("printing search", search);
      } catch (error) {
        console.error("Error adding user in group:", error);
      }
    } else if (type === "remove") {
      try {
        await removeUserToGroup.mutate();
        setTimeout(() => { }, 1000);
      } catch (error) {
        console.error("Error mutating user in group:", error);
      }
    }
  };

  if (type == "remove") {
    //the table where all the users are listed and we wanted to remove them
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden h-full">
        {/* Search Bar */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <div className="w-64">
              <SearchBar onSearch={setsearch} />
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto h-full">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Details</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {nonGroupUserSearchData && nonGroupUserSearchData.length > 0 ? (
                nonGroupUserSearchData.map((userValue, index) => (
                  <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                    {/* User Details Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 mr-4">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {userValue.user_name?.charAt(0)?.toUpperCase() || 'U'}
                            </span>
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {userValue.user_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {userValue.user_id}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Contact Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{userValue.email}</div>
                      <div className="text-sm text-gray-500">{userValue.phone}</div>
                    </td>

                    {/* Actions Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => {
                          refreshCount.current += 1;
                          handleClickUser("add");
                          setuserIdValue(userValue.user_id);
                        }}
                        className="inline-flex items-center p-2 text-sm font-medium text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-all duration-200"
                        title="Add to Group"
                      >
                        <PlusIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={3} className="px-6 py-12 text-center">
                    <Nothing
                      title="No Users Available"
                      para="There are currently no users available to add to this group."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  } else if (type == "all") {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden h-full">
        {/* Search Bar */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <div className="w-64">
              <SearchBar onSearch={setsearch2} />
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto h-full">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Details</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {groupUserSearchData && groupUserSearchData.length > 0 ? (
                groupUserSearchData?.map((userValue, index) => (
                  <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                    {/* User Details Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 mr-4">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {userValue.user_name?.charAt(0)?.toUpperCase() || 'U'}
                            </span>
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {userValue.user_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {userValue.user_id}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Contact Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{userValue.email}</div>
                      <div className="text-sm text-gray-500">{userValue.phone}</div>
                    </td>

                    {/* Actions Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => {
                          refreshCount.current = refreshCount.current - 1;
                          handleClickUser("remove");
                          setuserIdValue(userValue.user_id);
                        }}
                        className="inline-flex items-center p-2 text-sm font-medium text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all duration-200"
                        title="Remove from Group"
                      >
                        <MinusIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={3} className="px-6 py-12 text-center">
                    <Nothing
                      title="No Group Members"
                      para="This group currently has no members. Add users to get started."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  } else {
    return (
      <>
        <h1>Wrong type</h1>
      </>
    );
  }
};

export default AllUser;
