"use client";
import React, { useEffect, useState } from "react";
import Heading from "@/components/ui/Heading";
import Nothing from "@/components/ui/Nothing";

import { MinusIcon, PlusIcon, TrashIcon } from "@heroicons/react/24/outline";
import { useAddContentToGroup } from "@/hook/admin/usergroup/content/useAddContentToGroup";
import { AddRemoveContentGroup } from "@/types/LMSTypes";
import { useRemoveContentFromGroup } from "@/hook/admin/usergroup/content/useRemoveContentFromGroup";
import SearchBar from "../SearchBar";
import { useGetSearchFromGroupContentTable } from "@/hook/admin/usergroup/content/useGetSearchFromGroupContentTable";
import { useGetSearchFromAllContentInTable } from "@/hook/admin/usergroup/content/usegetSearchFormContentInGroup";


export default function AddRemoveContentForGroup({ type, dataforAll, groupId }: any) {
  const [search, setsearch] = useState("");
  const [groupIdValue, setGroupIdValue] = useState(groupId)
  const [contentIdValue, setcontentIdValue] = useState(Number)
  const [search2, setsearch2] = useState("");


  const newData: AddRemoveContentGroup = {
    content_id: contentIdValue,
    group_id: groupIdValue
  }

  const { data: SearchFromGroupContent } = useGetSearchFromGroupContentTable(groupIdValue, search);

  const { data: SearchFromAllContent } = useGetSearchFromAllContentInTable(groupIdValue, search2);



  const addingContentToGroup = useAddContentToGroup(newData);
  const removeContentToGroup = useRemoveContentFromGroup(contentIdValue, groupIdValue);

  const handleClickUser = async (type: string) => {

    if (type === "add") {
      try {
        await addingContentToGroup.mutate();

        setTimeout(() => {
        }, 1000);

      } catch (error) {
        console.error("Error adding user in group:", error);
      }
    } else if (type === "remove") {
      try {
        await removeContentToGroup.mutate();
        setTimeout(() => {
        }, 1000);
      } catch (error) {
        console.error("Error mutating user in group:", error);
      }
    }
  }



  // Utility function to ensure the input is an array.
  function ensureArray(input) {
    return Array.isArray(input) ? input : [];
  }

  // Usage in your component.
  const contentToMap = ensureArray(search2.trim() === '' ? dataforAll : SearchFromAllContent)

  if (type == "remove") {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden h-full">
        {/* Search Bar */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <div className="w-64">
              <SearchBar onSearch={setsearch} />
            </div>
          </div>
        </div>
        <div className="overflow-x-auto h-full">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Content Details</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topics</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Path</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {Array.isArray(SearchFromGroupContent) && SearchFromGroupContent.length > 0 ? (
                SearchFromGroupContent.map((item) => (
                  <tr key={item.content_id} className="hover:bg-gray-50 transition-colors duration-150">
                    {/* Content Details Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 mr-4">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {item.content_name?.charAt(0)?.toUpperCase() || 'C'}
                            </span>
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {item.content_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {item.content_id}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Description Column */}
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 truncate max-w-xs">
                        {item.content_description}
                      </div>
                    </td>

                    {/* Topics Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{item.topics}</div>
                    </td>

                    {/* File Path Column */}
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500 truncate max-w-xs">{item.file_path}</div>
                    </td>

                    {/* Actions Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => {
                          handleClickUser("remove");
                          setcontentIdValue(item.content_id);
                        }}
                        className="inline-flex items-center p-2 text-sm font-medium text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all duration-200"
                        title="Remove from Group"
                      >
                        <MinusIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center">
                    <Nothing
                      title="No Content Assigned"
                      para="This group currently has no content assigned."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  } else if (type == "all") {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden h-full">
        {/* Search Bar */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <div className="w-64">
              <SearchBar onSearch={setsearch2} />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto h-full">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Content Details</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topics</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Path</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {contentToMap.length > 0 ? (
                contentToMap.map((item, index) => (
                  <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                    {/* Content Details Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 mr-4">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {item.content_name?.charAt(0)?.toUpperCase() || 'C'}
                            </span>
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {item.content_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {item.content_id}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Description Column */}
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 truncate max-w-xs">
                        {item.content_description}
                      </div>
                    </td>

                    {/* Topics Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{item.topics}</div>
                    </td>

                    {/* File Path Column */}
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500 truncate max-w-xs">{item.file_path}</div>
                    </td>

                    {/* Actions Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => {
                          handleClickUser("add");
                          setcontentIdValue(item.content_id);
                        }}
                        className="inline-flex items-center p-2 text-sm font-medium text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-all duration-200"
                        title="Add to Group"
                      >
                        <PlusIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center">
                    <Nothing
                      title="No Content Available"
                      para="There are currently no content available to add to this group."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  } else {
    return (
      <>
        <h1>Wrong type</h1>
      </>
    );
  }
}
