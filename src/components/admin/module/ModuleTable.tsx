"use client";
import React, { useState, useEffect } from "react";
import SearchBar from "../SearchBar";
import { PlusIcon, MinusIcon, ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/outline";
import Heading from "@/components/ui/Heading";
import { AddRemoveModuleGroup } from "@/types/LMSTypes";
import { useRemoveModuleFromGroup } from "@/hook/admin/group/useRemoveModuleFromGroup";
import { useAddModuleToGroup } from "@/hook/admin/group/useAddModuleToGroup";
import Nothing from "@/components/ui/Nothing";
import { useGetSearchFromGroupModuleInTable } from "@/hook/admin/usergroup/module/useGetSearchFromGroupModuleInTable";
import { useGetSearchFromAllModuleInTable } from "@/hook/admin/usergroup/module/useGetSearchFromAllModuleInTable";

import { usePutUpdateModule } from "@/hook/admin/group/usePutUpdateModuleSeq";
import SubmitModal from "@/components/ui/SubmitModal";

const ModuleTable = ({ modules, type, groupId }) => {
  const [search, setSearch] = useState("");
  const [groupIdValue, setGroupIdValue] = useState(groupId);
  const [moduleIdValue, setModuleIdValue] = useState(Number);
  const [search2, setSearch2] = useState("");
  const [updatedModules, setUpdatedModules] = useState([]);
  const [showSubmitModal, setShowSubmitModal] = useState(false);

  const { data: SearchFromGroupModule } = useGetSearchFromGroupModuleInTable(groupIdValue, search);
  const { data: SearchFromAllModule } = useGetSearchFromAllModuleInTable(groupIdValue, search2);

  

  function convertIsoToCustomFormat(isoString) {
    const date = new Date(isoString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const customFormat = `${year}/${month}/${day} ${hours}:${minutes}`;
    return customFormat;
  }

  
  useEffect(() => {
    if (SearchFromGroupModule) {
      setUpdatedModules(SearchFromGroupModule.map((item, index) => ({ ...item, sequence: index + 1 })));
    }
  }, [SearchFromGroupModule]);

  const sequence = (updatedModules ? updatedModules.length : 0) + 1;
  const newData: AddRemoveModuleGroup = {
    module_id: moduleIdValue,
    group_id: groupIdValue,
    sequence: sequence,
  };

  const addingModuleToGroup = useAddModuleToGroup(newData);
  const updateModuleSeq = usePutUpdateModule();
  const removeModuleToGroup = useRemoveModuleFromGroup(moduleIdValue, groupIdValue);



  const handleMoveUp = (index) => {
    if (index === 0) return;
    const newUpdatedModules = [...updatedModules];
    [newUpdatedModules[index - 1], newUpdatedModules[index]] = [newUpdatedModules[index], newUpdatedModules[index - 1]];
    setUpdatedModules(newUpdatedModules.map((module, idx) => ({ ...module, sequence: idx + 1 })));
  };

  const handleMoveDown = (index) => {
    if (index === updatedModules.length - 1) return;
    const newUpdatedModules = [...updatedModules];
    [newUpdatedModules[index], newUpdatedModules[index + 1]] = [newUpdatedModules[index + 1], newUpdatedModules[index]];
    setUpdatedModules(newUpdatedModules.map((module, idx) => ({ ...module, sequence: idx + 1 })));
  };

  const handleClickUser = async (type) => {
    if (type === "add") {
      try {
        await addingModuleToGroup.mutateAsync();
        console.log("printing search", search);
      } catch (error) {
        console.error("Error adding user in group:", error);
      }
    } else if (type === "remove") {
      try {
        await removeModuleToGroup.mutateAsync();
      } catch (error) {
        console.error("Error mutating user in group:", error);
      }
    }
  };



  const handleSave = () => {
    updatedModules.forEach((module) => {
      const updateDetail = {
        module_id: module.module_id,
        group_id: groupIdValue,
        sequence: module.sequence,
      };
      updateModuleSeq.mutate(updateDetail, {
        onError: (error) => {
          console.error("Error updating sequence:", error);
        },
        onSuccess: (data) => {
          console.log("Sequence update success:", data);
          setShowSubmitModal(true);
        },
      });
    });
  };

  if (type === "remove") {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden h-full">
        {/* Search and Actions Bar */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="w-64">
              <SearchBar onSearch={setSearch} />
            </div>
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-sm hover:shadow-md transition-all duration-200"
              onClick={handleSave}
            >
              Save Sequence
            </button>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto h-full">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sequence</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Module Details</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {Array.isArray(updatedModules) && updatedModules.length > 0 ? (
                updatedModules.map((module, index) => (
                  <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                    {/* Sequence Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleMoveUp(index, addingModuleToGroup)}
                          className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
                          title="Move Up"
                        >
                          <ArrowUpIcon className="h-4 w-4" />
                        </button>
                        <span className="text-sm font-medium text-gray-900 min-w-[2rem] text-center">
                          {module.sequence}
                        </span>
                        <button
                          onClick={() => handleMoveDown(index, addingModuleToGroup)}
                          className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
                          title="Move Down"
                        >
                          <ArrowDownIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>

                    {/* Module Details Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 mr-4">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {module.module_name?.charAt(0)?.toUpperCase() || 'M'}
                            </span>
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {module.module_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {module.module_id}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Description Column */}
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 font-medium">
                        {module.module_headline}
                      </div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {module.module_description}
                      </div>
                    </td>

                    {/* Created By Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{module.created_by}</div>
                    </td>

                    {/* Actions Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => {
                          handleClickUser("remove");
                          setModuleIdValue(module.module_id);
                        }}
                        className="inline-flex items-center p-2 text-sm font-medium text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all duration-200"
                        title="Remove from Group"
                      >
                        <MinusIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center">
                    <Nothing
                      title="No Modules Assigned"
                      para="This group currently has no modules assigned."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Modals */}
        {showSubmitModal && (
          <SubmitModal modalName={" Save "} modalText={"The Sequence is Save "} type={1} onClose={()=>{setShowSubmitModal(false)}} />
        )}
      </div>
    );
  } else if (type == "add") {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden h-full">
        {/* Search Bar */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <div className="w-64">
              <SearchBar onSearch={setSearch2} />
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto h-full">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Module Details</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(Array.isArray(SearchFromAllModule) && SearchFromAllModule.length > 0) || search2.trim() === '' ? (
                (search2.trim() === '' ? modules : SearchFromAllModule).map((module, index) => (
                  <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                    {/* Module Details Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 mr-4">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {module.module_name?.charAt(0)?.toUpperCase() || 'M'}
                            </span>
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {module.module_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {module.module_id}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Description Column */}
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 font-medium">
                        {module.module_headline}
                      </div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {module.module_description}
                      </div>
                    </td>

                    {/* Created Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {convertIsoToCustomFormat(module.creation_date)}
                      </div>
                      <div className="text-sm text-gray-500">
                        By: {module.created_by}
                      </div>
                    </td>

                    {/* Actions Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => {
                          handleClickUser("add");
                          setModuleIdValue(module.module_id);
                        }}
                        className="inline-flex items-center p-2 text-sm font-medium text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-all duration-200"
                        title="Add to Group"
                      >
                        <PlusIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-6 py-12 text-center">
                    <Nothing
                      title="No Modules Available"
                      para="There are currently no modules available to add to this group."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  } else {
    return (
      <>
        <h1>Wrong type</h1>
      </>
    );
  }
}
export default ModuleTable;
