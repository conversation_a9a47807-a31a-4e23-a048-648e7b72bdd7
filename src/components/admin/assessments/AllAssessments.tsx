"use client";
import React, { useState, useRef } from "react";
import SearchBar from "../SearchBar";
import { PlusIcon, MinusIcon, DocumentTextIcon } from "@heroicons/react/24/outline";
import Heading from "@/components/ui/Heading";

import { AddRemoveAssessmentToGroup } from "@/types/LMSTypes";
import { useRemoveUserFromGroup } from "@/hook/admin/group/useRemoveUserFromGroup";
import { useRemoveAssessmentFromGroup } from "@/hook/admin/assessments/useRemoveAssessmentFromGroup";
import Nothing from "@/components/ui/Nothing";

import AddAssessmentModal from "@/components/admin/userGroup/AddAssessmentModal";
import SubmitModal from "@/components/ui/SubmitModal";
import { useGetGroupAssessment } from "@/hook/admin/usergroup/assessments/useGetGroupAssessment";
import { useGetNonGroupAssessment } from "@/hook/admin/usergroup/assessments/useGetNonGroupAssessment";
import { useQueryClient } from "@tanstack/react-query";

export default function AllAssessments({ type, groupId }: any) {
  const [search, setsearch] = useState("");
  const [groupIdValue, setGroupIdValue] = useState(groupId);
  const [groupAssessmentId, setGroupAssessmentId] = useState(Number);
  const [open, setOpen] = useState<boolean>(false);
  const [userIdValue, setuserIdValue] = useState(Number);
  const [search2, setsearch2] = useState("");
  const [first, setfirst] = useState(false)
  const [assessmentId, setAssessmentId] = useState<number>()
  const [assessmentName, setAssessmentName] = useState("")
  const [type1, setType1] = useState("")


  const assessment_name = useRef("")

  const [showSubmitModal, setShowSubmitModal] = useState(false);

  const onAddAssessment = () => {
    setOpen(false);
    setShowSubmitModal(true);
  };
  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
  };

  const newDetail: AddRemoveAssessmentToGroup = {
    group_assessment_id: groupAssessmentId,
  };

  console.log("search 1:-", search)
  console.log("search 2:-", search2)


  const { data: gettingAssessmentInGroup } = useGetGroupAssessment(groupId, search2)

  const { data: gettingAssessmentNotInGroup } = useGetNonGroupAssessment(groupId, search)

  const queryClient = useQueryClient();

  // if (type1 == "add") {
  //   queryClient.invalidateQueries({ queryKey: ['getNonGroupAssessment',groupId, search2] })
  // } else {
  //   queryClient.invalidateQueries({ queryKey: ['getAssessmentsInGroup',groupId, search] })
  // }


  function convertIsoToCustomFormat(isoString) {
    // Parse the ISO 8601 string
    const date = new Date(isoString);

    // Get the components of the date
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based, so we add 1
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    // Concatenate the components in the desired format
    const customFormat = `${year}/${month}/${day} ${hours}:${minutes}`;

    return customFormat;
  }


  console.log("data gettingAssessmentInGroup:-", gettingAssessmentInGroup)
  console.log("data gettingAssessmentNotInGroup:-", gettingAssessmentNotInGroup)

  const removeAssessmentToGroup = useRemoveAssessmentFromGroup(newDetail);

  const handleClickUser = async (type: string) => {
    if (type === "remove") {
      try {
        await removeAssessmentToGroup.mutate();
        queryClient.invalidateQueries({ queryKey: ['getNonGroupAssessment', groupId, search2] })
      } catch (error) {
        console.error("Error mutating assessment in group:", error);
      }
    } else {
      console.log("not working");
    }
  };

  if (type == "remove") {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden h-full">
        {/* Search Bar */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <div className="w-64">
              <SearchBar onSearch={setsearch} />
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto h-full">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assessment Details</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Settings</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {gettingAssessmentInGroup && gettingAssessmentInGroup.length > 0 ? (
                gettingAssessmentInGroup?.map((assessment, index) => (
                  <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                    {/* Assessment Details Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 mr-4">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center">
                            <DocumentTextIcon className="h-5 w-5 text-white" />
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {assessment.assessment_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {assessment.assessment_id}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Schedule Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        Start: {convertIsoToCustomFormat(assessment.start_date)}
                      </div>
                      <div className="text-sm text-gray-500">
                        End: {convertIsoToCustomFormat(assessment.end_date)}
                      </div>
                    </td>

                    {/* Settings Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        Max Attempts: {assessment.max_attempts}
                      </div>
                      <div className="text-sm text-gray-500">
                        By: {assessment.assigned_by}
                      </div>
                    </td>

                    {/* Actions Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => {
                          handleClickUser("remove");
                          setGroupAssessmentId(assessment.group_assessment_id);
                          setType1("remove")
                        }}
                        className="inline-flex items-center p-2 text-sm font-medium text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all duration-200"
                        title="Remove from Group"
                      >
                        <MinusIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-6 py-12 text-center">
                    <Nothing
                      title="No Assessments Assigned"
                      para="This group currently has no assessments assigned."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  } else if (type == "add") {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden h-full">
        {/* Search Bar */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <div className="w-64">
              <SearchBar onSearch={setsearch2} />
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto h-full">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assessment Details</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Configuration</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {gettingAssessmentNotInGroup && gettingAssessmentNotInGroup.length > 0 ? (
                gettingAssessmentNotInGroup?.map((assessment, index) => (
                  <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                    {/* Assessment Details Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 mr-4">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center">
                            <DocumentTextIcon className="h-5 w-5 text-white" />
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {assessment.assessment_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {assessment.assessment_id}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Configuration Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        Time: {(assessment.total_time_allowed/60).toFixed(2)} min
                      </div>
                      <div className="text-sm text-gray-500">
                        Marks: {assessment.total_marks}
                      </div>
                    </td>

                    {/* Source Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{assessment.source}</div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {assessment.instructions}
                      </div>
                    </td>

                    {/* Actions Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => {
                          handleClickUser("add");
                          setuserIdValue(assessment.user_id);
                          setAssessmentId(assessment.assessment_id)
                          setAssessmentName(assessment.assessment_name)
                          setOpen(true)
                          setfirst(true)
                          setType1("add")
                        }}
                        className="inline-flex items-center p-2 text-sm font-medium text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-all duration-200"
                        title="Add to Group"
                      >
                        <PlusIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-6 py-12 text-center">
                    <Nothing
                      title="No Assessments Available"
                      para="There are currently no assessments available to add to this group."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Modals */}
        {showSubmitModal && (
          <SubmitModal
            modalName="Assessment Added Success"
            onClose={onCloseSubmitModal}
          />
        )}
        {first && <AddAssessmentModal open={open} onClose={() => setOpen(false)} group_Id={groupId} assessment_Id={assessmentId ? assessmentId : 0} assessmentName={assessmentName} />}
      </div>
    );
  } else {
    return (
      <>
        <h1>Wrong type</h1>
      </>
    );
  }
}
