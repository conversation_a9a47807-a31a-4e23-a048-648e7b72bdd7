"use client";

import React, { useEffect, useState } from "react";
import AllAssessment from "@/components/admin/assessments/AllAssessments";
import { useGetAssessmentByGroupId } from "@/hook/admin/group/useGetAssessmentByGroupId";
import { useRouter, useSearchParams } from "next/navigation";

// import { useGetAllAssessmentsForGroup } from "@/hook/assessments/useGetAllAssessmentForGroup";
import { useGetAssessmentsNotInGroup } from "@/hook/admin/group/useGetAssessmentsNotInGroup";
import CreateModal from "@/components/admin/assessments/CreateModal";
import Button from "@/components/ui/Button";


export default function AdminAssessmentPage() {

  const searchParams = useSearchParams();
  const group_id = searchParams.get("groupId");
  const [groupId, setGroupId] = useState(parseInt(group_id || ''));
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const router = useRouter();

  const onSaveClickFunction=()=>{
    setShowSubmitModal(true)
  }

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
    router.push("/admin/usergroup");
  };

  return (
    <main className="w-full flex flex-col p-4 relative overflow-auto h-full bg-gray-50">
      <div className="w-full max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Manage Group Assessments</h1>
              <p className="text-sm text-gray-500 mt-1">Add or remove assessments from the selected group</p>
            </div>
          </div>
        </div>

        {/* Assessments Management Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Group Assessments */}
          <div className="bg-white rounded-xl shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Group Assessments</h2>
              <p className="text-sm text-gray-500">Assessments currently assigned to this group</p>
            </div>
            <div className="p-6">
              <AllAssessment type={"remove"} groupId={groupId} />
            </div>
          </div>

          {/* Available Assessments */}
          <div className="bg-white rounded-xl shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Available Assessments</h2>
              <p className="text-sm text-gray-500">Assessments not assigned to this group</p>
            </div>
            <div className="p-6">
              <AllAssessment type={"add"} groupId={groupId} />
            </div>
          </div>
        </div>

        {/* Actions Section */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-center">
            <button
              onClick={onSaveClickFunction}
              className="inline-flex items-center px-6 py-3 text-sm font-medium rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-[1.02]"
            >
              Save & Back
            </button>
          </div>
        </div>
      </div>

      {showSubmitModal && (
        <CreateModal modulename="Changes have been successfully saved" onClose={onCloseSubmitModal} />
      )}
    </main>
  );
}
