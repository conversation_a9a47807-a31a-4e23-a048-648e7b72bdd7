"use client"
import React, { useState, useEffect, useRef } from "react";
import { useGetAllGroups } from "@/hook/admin/group/useGetAllGroups";
import SearchBar from "@/components/admin/SearchBar";
import ToolTip from "@/components/ui/ToolTip";

//Libraries
import { UserPlusIcon, UserGroupIcon, InformationCircleIcon } from "@heroicons/react/24/outline";
import { toast } from 'react-toastify';

//Hooks
import { useSearchUserGroups } from "@/hook/user/useSearchUserGroups";

//Components
import GroupModal from "@/components/admin/userGroup/GroupModal";
import Heading from "@/components/ui/Heading";
import Button from "@/components/ui/Button";
import UserGroupTable from "@/components/admin/userGroup/UserGroupTable";
import SubmitModal from "@/components/ui/SubmitModal";
import { useAddUserToGroupBulk } from "@/hook/admin/group/useAddUserToGroupBulk";
import BulkQuesModal from "@/components/admin/BulkQuesModal";
import { getUser, setFirstLogin} from "@/api/user.localStorage";

const defaultGroupID = 0;
const defaultType = "createGroup";
const mockGroups = [
  {
    group_id: 1,
    group_admin_user_id: 101,
    group_name: "Frontend Team",
    isactive: 1,
    created_by: "Admin",
    total_member: "4",
  },
  {
    group_id: 2,
    group_admin_user_id: 102,
    group_name: "Backend Team",
    isactive: 1,
    created_by: "Admin",
    total_member: "3",
  },
  {
    group_id: 3,
    group_admin_user_id: 103,
    group_name: "QA Team",
    isactive: 0,
    created_by: "Admin",
    total_member: "2",
  },
];
const Page = () => {
  const user = getUser()
  const isFirstTime = useRef(user?.firstLogin)
  const [search, setSearch] = useState<string>("");
  const [file, setFile] = useState<File | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  //Using Mock Data - Uncomment below line to use API
  //const { data: userValues, isLoading, isError } = useGetAllGroups();
  //const { data: searchedUserGroups } = useSearchUserGroups(search);
  const [showAddGroupModal, setShowAddGroupModal] = useState(false);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [showBulkUserPopup, setShowBulkUserPopup] = useState(false);
  const [bulkUserPopupMessage, setBulkUserPopupMessage] = useState("");
  const [modalName, setModalName] = useState("");
  const fileInputRef = useRef(null);
  const [isHovering, setIsHovering] = useState(false);

  const sendingBulkUserToGroupUpload = useAddUserToGroupBulk();

  const handleSubmit = async (event?: React.ChangeEvent<HTMLInputElement>) => {
    console.log("Running pre-event");
    if (event) {
      let uploadedFile = event.target.files?.[0];
      console.log("Uploaded file:", uploadedFile);
      if (uploadedFile && uploadedFile.type === "text/csv") {
        setFile(uploadedFile);
        event.target.value = null;
      } else {
        setBulkUserPopupMessage("Please upload a valid CSV file.");
        setModalName("Error");
        setShowBulkUserPopup(true);
        return;
      }
    }
  };

  useEffect(() => {
    console.log("Running pre");
    if (file) {
      console.log("File ready for upload:", file);
      sendingBulkUserToGroupUpload.mutateAsync(file) // Pass the file data here
        .then(() => {
          setFile(null);
          setBulkUserPopupMessage("Bulk user addition successful!");
          setModalName("Success");
          setShowBulkUserPopup(true);
          console.log("Mutation success");
        })
        .catch((error) => {
          setBulkUserPopupMessage("Error while adding bulk users.");
          setModalName("Error");
          setShowBulkUserPopup(true);
          console.error("Mutation error:", error);
        });
    }
  }, [file]);

  
  useEffect(() => {
    setFirstLogin(true)
  }, [user?.firstLogin])

   // Uncomment below lines to use API
  // if (isLoading) {
  //   return <div>Loading...</div>;
  // }

  // if (isError) {
  //   return <div>Error fetching user groups data</div>;
  // }

  const handleSearch = (groupSearch: string) => {
    console.log("Group search:", groupSearch);
    setSearch(groupSearch);
  };

  const openModalForNewGroup = () => {
    setIsModalOpen(true);
    setShowAddGroupModal(true);
  };

  const onCloseAddGroupModal = () => {
    setShowAddGroupModal(false);
  };

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
  };

  const onCloseBulkUserPopup = () => {
    setShowBulkUserPopup(false);
  };

  const onSubmitAddGroupModal = () => {
    setShowAddGroupModal(false);
    setShowSubmitModal(true);
  };


  return (
    <main className="w-full flex flex-col p-4 relative overflow-auto h-full bg-gray-50">
      <div className="w-full max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <Heading pgHeading="User Groups" />
              <button
                onClick={() => toast.info('Manage user groups, create new groups, and organize users efficiently.')}
                className="text-gray-500 hover:text-blue-600 transition-colors"
                aria-label="Information"
              >
                <InformationCircleIcon className="w-5 h-5" />
              </button>
            </div>
            <div className="text-sm text-gray-500">
              {/* Add group count here when API is connected */}
              Groups Management
            </div>
          </div>
        </div>

        {/* Search and Actions Section */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
            <div className="flex-1 max-w-md">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Groups
              </label>
              <SearchBar onSearch={handleSearch} />
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <input
                type="file"
                accept="text/csv"
                ref={fileInputRef}
                style={{ display: "none" }}
                onChange={handleSubmit}
              />

              <button
                onClick={() => fileInputRef.current?.click()}
                className="inline-flex items-center px-6 py-3 text-sm font-medium rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-[1.02]"
              >
                <UserGroupIcon className="h-5 w-5 mr-2" />
                Bulk User Upload
              </button>

              <button
                onClick={openModalForNewGroup}
                className="inline-flex items-center px-6 py-3 text-sm font-medium rounded-lg bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800 text-white shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-[1.02]"
              >
                <UserPlusIcon className="h-5 w-5 mr-2" />
                Create Group
              </button>
            </div>
          </div>

          {/* Upload Instructions */}
          <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-start">
              <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 mb-1">Group Management</h4>
                <p className="text-sm text-blue-700">
                  Create and manage user groups to organize your users effectively. Use bulk upload to add multiple users to groups at once.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Groups List Section */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-medium text-gray-900">
              {search ? `Search Results for "${search}"` : 'All Groups'}
            </h2>
          </div>

          <div className="min-h-[400px]">
            <UserGroupTable />
          </div>
        </div>
      </div>

      {/* Modals */}
      {showAddGroupModal && isFirstTime.current && (
        <GroupModal onClose={onCloseAddGroupModal} onSubmit={onSubmitAddGroupModal} groupID={defaultGroupID} type={defaultType} />
      )}
      {showSubmitModal && (
        <SubmitModal modalName="Group is Created Success" onClose={onCloseSubmitModal} />
      )}
      {showBulkUserPopup && (
        <BulkQuesModal modalName={modalName} modalText={bulkUserPopupMessage} onClose={onCloseBulkUserPopup} />
      )}
    </main>
  );
};

export default Page;
