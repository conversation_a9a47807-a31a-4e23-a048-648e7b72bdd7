"use client"
import ModuleTable from '@/components/admin/module/ModuleTable';
import React, { useState } from 'react'
import { useRouter, useSearchParams } from "next/navigation";
import { useGetModulesByGroupId } from '@/hook/admin/module/useGetModulesByGroupId';
import { useGetModulesNotInGroup } from '@/hook/admin/group/useGetModulesNotInGroup';
import Button from '@/components/ui/Button';
import CreateModal from '@/components/admin/assessments/CreateModal';



export default function AddremovemodulePage() {

  const searchParams = useSearchParams();
  const group_id = searchParams.get("groupId");
  const [groupId, setGroupId] = useState(parseInt(group_id || ''));

  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const router = useRouter();

  const onSaveClickFunction=()=>{
    setShowSubmitModal(true)
  }

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
    router.push("/admin/usergroup");
  };


  const { data: getAllModuleData, isLoading: getAllModuleLoading, isError: getAllModuleError } = useGetModulesNotInGroup(groupId);

  const { data: getModulesByGroupIdData, isLoading: getModulesByGroupIdLoading, isError: getModulesByGroupIdError } = useGetModulesByGroupId(groupId);



  if (getAllModuleLoading || getModulesByGroupIdLoading) {
    return (
      <main className="w-full flex flex-col p-4 relative overflow-auto h-full bg-gray-50">
        <div className="w-full max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-sm p-6 flex items-center justify-center">
            <div className="flex items-center space-x-3">
              <div className="animate-spin h-6 w-6 border-3 border-blue-500 border-t-transparent rounded-full"></div>
              <span className="text-blue-600 font-medium">Loading modules...</span>
            </div>
          </div>
        </div>
      </main>
    );
  }

  if (getAllModuleError || getModulesByGroupIdError) {
    return (
      <main className="w-full flex flex-col p-4 relative overflow-auto h-full bg-gray-50">
        <div className="w-full max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="text-center py-8">
              <div className="rounded-full bg-red-50 p-3 mx-auto w-16 h-16 flex items-center justify-center mb-4">
                <span className="text-red-600 text-xl">⚠</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Modules</h3>
              <p className="text-gray-600">There was an error fetching the module data. Please try again.</p>
            </div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="w-full flex flex-col p-4 relative overflow-auto h-full bg-gray-50">
      <div className="w-full max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Manage Group Modules</h1>
              <p className="text-sm text-gray-500 mt-1">Add or remove modules from the selected group</p>
            </div>
          </div>
        </div>

        {/* Modules Management Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Group Modules */}
          <div className="bg-white rounded-xl shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Group Modules</h2>
              <p className="text-sm text-gray-500">Modules currently assigned to this group</p>
            </div>
            <div className="p-6">
              <ModuleTable type={"remove"} modules={getModulesByGroupIdData} groupId={groupId} />
            </div>
          </div>

          {/* Available Modules */}
          <div className="bg-white rounded-xl shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Available Modules</h2>
              <p className="text-sm text-gray-500">Modules not assigned to this group</p>
            </div>
            <div className="p-6">
              <ModuleTable type={"add"} modules={getAllModuleData} groupId={groupId} />
            </div>
          </div>
        </div>

        {/* Actions Section */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-center">
            <button
              onClick={onSaveClickFunction}
              className="inline-flex items-center px-6 py-3 text-sm font-medium rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-[1.02]"
            >
              Save & Back
            </button>
          </div>
        </div>
      </div>

      {showSubmitModal && (
        <CreateModal modulename="Changes have been successfully saved" onClose={onCloseSubmitModal} />
      )}
    </main>
  );
}
