"use client"
import React, { useEffect, useState } from "react";
import { useGetUsersNotInGroup } from "@/hook/admin/group/useGetUsersNotInGroup";
import { useRouter, useSearchParams } from "next/navigation";

import AllUser from "@/components/admin/userGroup/AllUser";
import Heading from "@/components/ui/Heading";
import { useGetUsersInGroup } from "@/hook/admin/group/useGetUsersInGroup";
import Button from "@/components/ui/Button";
import CreateModal from "@/components/admin/assessments/CreateModal";
import { useGetUserSearch } from "@/hook/admin/usergroup/alluser/useGetUserSearch";
import { useGetNonUserSearch } from "@/hook/admin/usergroup/alluser/useGetNonUserSearch";


const Page = () => {
  const searchParams = useSearchParams();
  const group_id = searchParams.get("groupId");
  const group_name = searchParams.get("groupName");
  const [groupId, setGroupId] = useState(parseInt(group_id || ''));
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [search, setsearch] = useState("");
  const [search2, setsearch2] = useState("");
  const router = useRouter();

  const onSaveClickFunction=()=>{
    setShowSubmitModal(true)
  }

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
    router.push("/admin/usergroup");
  };
  const { data: groupUserSearchData } = useGetUserSearch(groupId, search2)
  const { data: nonGroupUserSearchData } = useGetNonUserSearch(groupId, search)

  console.log("nonGroupUserSearchData:-", nonGroupUserSearchData)
  console.log("groupUserSearchData:-", groupUserSearchData)

  return (
    <main className="w-full flex flex-col p-4 relative overflow-auto h-full bg-gray-50">
      <div className="w-full max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Manage Group Users</h1>
              <p className="text-sm text-gray-500 mt-1">Add or remove users from the selected group</p>
            </div>
          </div>
        </div>

        {/* Users Management Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Available Users */}
          <div className="bg-white rounded-xl shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Available Users</h2>
              <p className="text-sm text-gray-500">Users not in this group</p>
            </div>
            <div className="p-6">
              <AllUser type={"all"} groupId={groupId} />
            </div>
          </div>

          {/* Group Members */}
          <div className="bg-white rounded-xl shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Group Members</h2>
              <p className="text-sm text-gray-500">Users currently in this group</p>
            </div>
            <div className="p-6">
              <AllUser type={"remove"} groupId={groupId} />
            </div>
          </div>
        </div>

        {/* Actions Section */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-center">
            <button
              onClick={onSaveClickFunction}
              className="inline-flex items-center px-6 py-3 text-sm font-medium rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-[1.02]"
            >
              Save & Back
            </button>
          </div>
        </div>
      </div>

      {showSubmitModal && (
        <CreateModal modulename="Changes have been successfully saved" onClose={onCloseSubmitModal} />
      )}
    </main>
  );
}


export default Page;